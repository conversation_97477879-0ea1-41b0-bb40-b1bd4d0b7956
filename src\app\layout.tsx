import type { Metada<PERSON> } from "next";
import "../styles/global.css";
import { Inter, Libre_Baskerville } from "next/font/google";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import AppProvider from "@/app/app-provider";
import NextTopLoader from "nextjs-toploader";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { SettingProvider } from "@/context/SettingContext";
// import DisableDevTools from "@/components/Security/DisableDevTools"; // DISABLED
import SafeStyleOverride from "@/components/Security/SafeStyleOverride";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
});

const libre_baskerville = Libre_Baskerville({
  subsets: ["latin"],
  style: ["normal", "italic"],
  weight: ["400", "700"],
  variable: "--libre-baskerville",
  display: "swap",
});

export const metadata: Metadata = {
  title: "CỔNG THÔNG TIN ĐIỆN TỬ TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH",
  description:
    "CỔNG THÔNG TIN ĐIỆN TỬ TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${inter.className} ${libre_baskerville.className}`}
      data-theme="light"
    >
        <meta name="viewport" content="width=device-width, initial-scale=1.0, minimal-ui, shrink-to-fit=no, viewport-fit=cover" />
      <head>
        <meta name="application-name" content="" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta
          name="apple-mobile-web-app-title"
          content="Báo VnExpress - Báo tiếng Việt nhiều người xem nhất"
        />
        <link rel="manifest" href="/manifest.json" />
        {/* <script src="/disable-console.js" defer></script> DISABLED */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Override user agent stylesheet for ::marker */
            ::marker {
              unicode-bidi: normal !important;
              font-variant-numeric: normal !important;
              text-transform: inherit !important;
              text-indent: inherit !important;
              text-align: inherit !important;
              text-align-last: inherit !important;
              content: none !important;
              display: none !important;
            }

            ul, ol {
              list-style: none !important;
            }

            ul::marker, ol::marker, li::marker {
              content: none !important;
              display: none !important;
            }
          `
        }} />
      </head>

      <body className="overflow-x-hidden antialiased w-screen">
        {/* <DisableDevTools /> DISABLED */}
        <SafeStyleOverride />
        <GoogleOAuthProvider clientId={process.env.GOOGLE_CLIENT_ID || ""}>
          <AppProvider>
          <SettingProvider>
            <ToastContainer
              position="bottom-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
            <Header />
            <div className="main dark:bg-gray-900 text-gray-800 dark:text-white">
              <NextTopLoader />
              {children}
            </div>
            <Footer />
            </SettingProvider>
          </AppProvider>
        </GoogleOAuthProvider>
      </body>
   
    </html>
  );
}
