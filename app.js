const { createServer } = require('http');
const { parse } = require('url');
const path = require('path');
const next = require('next');
const express = require('express');

// Load environment variables
require('dotenv').config();

const dev = process.env.NODE_ENV !== 'production';
const nextApp = next({ dev });
const handle = nextApp.getRequestHandler();

// Import the backend Express app
const backendApp = require('./server/backend-app');

nextApp.prepare().then(() => {
  const server = express();

  // Add error handling middleware
  server.use((err, req, res, next) => {
    console.error('🚨 Server Error:', err);
    res.status(500).json({
      error: 'Internal Server Error',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
  });

  // Debug middleware to log all requests
  server.use((req, res, next) => {
    console.log(`🔍 Request: ${req.method} ${req.url}`);
    next();
  });

  // Serve static files from public directory FIRST
  server.use(express.static('public'));

  // Serve uploaded files
  server.use('/uploads', express.static('./server/uploads'));

  // Handle specific static files that might be missed
  server.get('/favicon.ico', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'favicon.ico'));
  });

  server.get('/manifest.json', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'manifest.json'));
  });

  // Mount backend API routes - the backend app handles its own routing
  server.use('/api', (req, res, next) => {
    console.log(`🔍 API Request: ${req.method} ${req.path}`);

    // Special cases: These routes should go to Next.js for cookie handling
    const nextjsAuthRoutes = ['/auth', '/auth/logout'];
    const isNextjsAuthRoute = nextjsAuthRoutes.some(route =>
      (route === '/auth' && req.path === '/auth' && req.method === 'POST') ||
      (route === '/auth/logout' && req.path === '/auth/logout')
    );

    if (isNextjsAuthRoute) {
      console.log(`📤 Forwarding to Next.js (auth route): ${req.path}`);
      return next();
    }

    // Check if this is a backend API route
    const backendRoutes = [
      '/auth/login', '/auth/signup', '/auth/verify-code', '/auth/verify-app-code',
      '/auth/check-code', '/auth/app-forgot-pass', '/auth/app-reset-pass', '/auth/user',
      '/administrator', '/setting', '/media', '/post_cat',
      '/post', '/menu', '/video', '/page', '/files', '/leaders', '/departments'
    ];

    const isBackendRoute = backendRoutes.some(route => req.path.startsWith(route));
    console.log(`🎯 Is backend route: ${isBackendRoute} for path: ${req.path}`);

    if (isBackendRoute) {
      console.log(`📤 Forwarding to backend: ${req.path}`);
      return backendApp(req, res, next);
    } else {
      console.log(`📤 Forwarding to Next.js: ${req.path}`);
      // Let Next.js handle its own API routes
      return next();
    }
  });

  // Handle Next.js requests (including its own API routes)
  server.all('*', (req, res) => {
    const parsedUrl = parse(req.url, true);
    return handle(req, res, parsedUrl);
  });

  const port = process.env.PORT || 3000;

  createServer(server).listen(port, (err) => {
    if (err) {
      console.error('🚨 Failed to start server:', err);
      throw err;
    }
    console.log(`🚀 Unified server ready on http://localhost:${port}`);
    console.log(`📱 Frontend: Next.js`);
    console.log(`🔧 Backend: Express.js + MongoDB`);
    console.log(`📁 Uploads: /uploads`);
    console.log(`🔗 Backend API: /api/*`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔗 MongoDB: ${process.env.MONGO_URL}`);
  });
}).catch((err) => {
  console.error('🚨 Failed to prepare Next.js app:', err);
  process.exit(1);
});
