const express = require('express');

const router = express.Router();
const passport = require('passport');
const menuController = require('../controllers/menu');
const { menuValidation, mongoIdValidation } = require('../middleware/inputValidation');


router.get("/libaries", passport.authenticate('user',{session: false}), menuController.getLibaries);

router.post('/', passport.authenticate('user', { session: false }), menuValidation, menuController.createMenu);

router.post("/get-all", passport.authenticate('user', { session: false }),  menuController.getMenu)

router.get("/:id", mongoIdValidation, menuController.getOneMenu)
  
router.put("/edit", (req, res, next) => {
  console.log("=== MENU EDIT ROUTE HIT ===");
  console.log("Method:", req.method);
  console.log("URL:", req.url);
  console.log("Body:", req.body);
  next();
}, passport.authenticate('user', { session: false }), menuController.editMenu);

router.delete("/:id", passport.authenticate('user', { session: false }), mongoIdValidation, menuController.deleteMenu) 


module.exports = router;
