const express = require("express");
const router = express.Router();
const passport = require("passport");
const {
  getPublicLeaders,
  getAllLeaders,
  getLeader,
  createLeader,
  updateLeader,
  deleteLeader,
  toggleLeaderStatus,
  reorderLeaders
} = require("../controllers/leader");
const { verifyAdmin } = require("../middleware/is-admin");

// Public routes
router.get("/public", getPublicLeaders);

// Admin routes - require authentication
router.get("/", passport.authenticate('user', { session: false }), verifyAdmin, getAllLeaders);
router.get("/:id", passport.authenticate('user', { session: false }), verifyAdmin, getLeader);
router.post("/", passport.authenticate('user', { session: false }), verifyAdmin, createLeader);
router.put("/:id", passport.authenticate('user', { session: false }), verifyAdmin, updateLeader);
router.delete("/:id", passport.authenticate('user', { session: false }), verifyAdmin, deleteLeader);
router.patch("/:id/toggle", passport.authenticate('user', { session: false }), verifyAdmin, toggleLeaderStatus);
router.put("/reorder", passport.authenticate('user', { session: false }), verifyAdmin, reorderLeaders);

module.exports = router;
